# SOLANA Price Monitor - OKX API

A Python program that monitors SOLANA (SOL) price in real-time using the OKX exchange API. The program fetches and displays price data every 10 seconds. Two versions are available:

- **Full Version** (`solana_price_monitor.py`): Rich, formatted display with detailed information
- **Simple Version** (`simple_solana_monitor.py`): Lightweight, one-line output format

## Features

- 🚀 Real-time SOLANA price monitoring
- 📊 Comprehensive price data display including:
  - Current price in USDT
  - 24-hour price change (amount and percentage)
  - 24-hour high, low, and open prices
  - 24-hour trading volume
  - Last update timestamp
- 🎨 Clean, formatted terminal output with color indicators
- ⚡ Updates every 10 seconds
- 🛡️ Error handling and connection retry logic
- 🌐 Uses OKX public API (no authentication required)

## Requirements

- Python 3.6 or higher
- Internet connection
- Required Python packages (see requirements.txt)

## Installation

1. Clone or download this repository
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### Full Version (Detailed Display)

Run the main program from the command line:

```bash
python solana_price_monitor.py
```

### Simple Version (One-line Display)

Run the simple version for a lightweight output:

```bash
python simple_solana_monitor.py
```

Both programs will:
1. Test the API connection
2. Display the current SOLANA price
3. Start the monitoring loop with updates every 10 seconds
4. Continue running until you press `Ctrl+C` to stop

## Sample Output

### Full Version Output
```
╔══════════════════════════════════════════════════════════════╗
║                    SOLANA (SOL) Price Monitor               ║
╠══════════════════════════════════════════════════════════════╣
║ Current Price:    $231.9700 USDT                            ║
║ 24h Change:       🔴 -4.2700 (-1.81%)                       ║
║                                                              ║
║ 24h High:         $237.0500 USDT                            ║
║ 24h Low:          $230.1400 USDT                            ║
║ 24h Open:         $236.2400 USDT                            ║
║                                                              ║
║ 24h Volume:       696,729.28 SOL                            ║
║ 24h Volume (USD): $163,047,198.38                           ║
║                                                              ║
║ Last Update:      2025-01-16 14:33:12                       ║
║ Exchange:         OKX                                        ║
╚══════════════════════════════════════════════════════════════╝
```

### Simple Version Output
```
SOLANA Price Monitor (Simple Version)
Press Ctrl+C to stop
--------------------------------------------------
[15:54:57] SOL: $232.3200 | 24h: -3.9700 (-1.68%) | H: $237.0500 | L: $230.1400
[15:55:07] SOL: $232.2400 | 24h: -3.7200 (-1.58%) | H: $237.0500 | L: $230.1400
[15:55:17] SOL: $232.3500 | 24h: -3.6100 (-1.53%) | H: $237.0500 | L: $230.1400
```

## API Information

This program uses the OKX public API endpoint:
- **Endpoint**: `https://www.okx.com/api/v5/market/ticker`
- **Instrument**: `SOL-USDT`
- **Rate Limit**: No authentication required for public endpoints
- **Documentation**: [OKX API Documentation](https://www.okx.com/okx-api)

## Error Handling

The program includes robust error handling for:
- Network connection issues
- API response errors
- JSON parsing errors
- Keyboard interruption (Ctrl+C)

If an error occurs, the program will display an error message and either retry (for network issues) or exit gracefully.

## Customization

You can easily modify the program to:
- Change the update interval (modify the `interval` parameter in `run_monitor()`)
- Monitor different cryptocurrencies (change the `instrument_id` in the `__init__` method)
- Adjust the display format (modify the `format_price_data()` method)
- Add additional data fields from the API response

## License

This project is open source and available under the MIT License.

## Disclaimer

This tool is for informational purposes only. Cryptocurrency prices are highly volatile and can change rapidly. Always do your own research before making any investment decisions.
