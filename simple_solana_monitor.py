#!/usr/bin/env python3
"""
Simple SOLANA Price Monitor using OKX API
A lightweight version that displays basic price information every 10 seconds.
"""

import requests
import time
import json
from datetime import datetime
import sys


def get_solana_price():
    """Fetch current SOLANA price from OKX API"""
    try:
        url = "https://www.okx.com/api/v5/market/ticker"
        params = {"instId": "SOL-USDT"}
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        if data.get("code") == "0" and data.get("data"):
            return data["data"][0]
        else:
            print(f"API Error: {data.get('msg', 'Unknown error')}")
            return None
            
    except Exception as e:
        print(f"Error fetching price: {e}")
        return None


def display_price(data):
    """Display price information in a simple format"""
    if not data:
        return
    
    current_price = float(data.get("last", 0))
    open_24h = float(data.get("open24h", 0))
    high_24h = float(data.get("high24h", 0))
    low_24h = float(data.get("low24h", 0))
    
    # Calculate 24h change
    change_24h = current_price - open_24h
    change_percent = (change_24h / open_24h * 100) if open_24h > 0 else 0
    
    # Format timestamp
    timestamp = int(data.get("ts", 0))
    dt = datetime.fromtimestamp(timestamp / 1000)
    
    # Display information
    print(f"[{dt.strftime('%H:%M:%S')}] SOL: ${current_price:.4f} | "
          f"24h: {change_24h:+.4f} ({change_percent:+.2f}%) | "
          f"H: ${high_24h:.4f} | L: ${low_24h:.4f}")


def main():
    """Main monitoring loop"""
    print("SOLANA Price Monitor (Simple Version)")
    print("Press Ctrl+C to stop")
    print("-" * 50)
    
    try:
        while True:
            price_data = get_solana_price()
            display_price(price_data)
            time.sleep(10)
            
    except KeyboardInterrupt:
        print("\nMonitor stopped.")
        sys.exit(0)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
