#!/usr/bin/env python3
"""
SOLANA Price Monitor using OKX API
Monitors SOL-USDT price every 10 seconds and displays real-time data.
"""

import requests
import time
import json
from datetime import datetime
from typing import Dict, Optional
import sys


class SolanaMonitor:
    """Monitor SOLANA price using OKX API"""
    
    def __init__(self):
        self.base_url = "https://www.okx.com/api/v5"
        self.ticker_endpoint = "/market/ticker"
        self.instrument_id = "SOL-USDT"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'SolanaMonitor/1.0',
            'Accept': 'application/json'
        })
        
    def get_price_data(self) -> Optional[Dict]:
        """
        Fetch current SOLANA price data from OKX API
        
        Returns:
            Dict containing price data or None if error
        """
        try:
            url = f"{self.base_url}{self.ticker_endpoint}"
            params = {"instId": self.instrument_id}
            
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get("code") == "0" and data.get("data"):
                return data["data"][0]
            else:
                print(f"API Error: {data.get('msg', 'Unknown error')}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"Network error: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            return None
        except Exception as e:
            print(f"Unexpected error: {e}")
            return None
    
    def format_price_data(self, data: Dict) -> str:
        """
        Format price data for display
        
        Args:
            data: Price data dictionary from API
            
        Returns:
            Formatted string for display
        """
        try:
            current_price = float(data.get("last", 0))
            open_24h = float(data.get("open24h", 0))
            high_24h = float(data.get("high24h", 0))
            low_24h = float(data.get("low24h", 0))
            volume_24h = float(data.get("vol24h", 0))
            volume_ccy_24h = float(data.get("volCcy24h", 0))
            
            # Calculate 24h change
            change_24h = current_price - open_24h
            change_percent = (change_24h / open_24h * 100) if open_24h > 0 else 0
            
            # Format timestamp
            timestamp = int(data.get("ts", 0))
            dt = datetime.fromtimestamp(timestamp / 1000)
            
            # Color coding for price change
            change_color = "🟢" if change_24h >= 0 else "🔴"
            change_sign = "+" if change_24h >= 0 else ""
            
            formatted_output = f"""
╔══════════════════════════════════════════════════════════════╗
║                    SOLANA (SOL) Price Monitor               ║
╠══════════════════════════════════════════════════════════════╣
║ Current Price:    ${current_price:,.4f} USDT                ║
║ 24h Change:       {change_color} {change_sign}{change_24h:,.4f} ({change_sign}{change_percent:,.2f}%)     ║
║                                                              ║
║ 24h High:         ${high_24h:,.4f} USDT                     ║
║ 24h Low:          ${low_24h:,.4f} USDT                      ║
║ 24h Open:         ${open_24h:,.4f} USDT                     ║
║                                                              ║
║ 24h Volume:       {volume_24h:,.2f} SOL                     ║
║ 24h Volume (USD): ${volume_ccy_24h:,.2f}                    ║
║                                                              ║
║ Last Update:      {dt.strftime('%Y-%m-%d %H:%M:%S')}        ║
║ Exchange:         OKX                                        ║
╚══════════════════════════════════════════════════════════════╝
            """
            
            return formatted_output.strip()
            
        except (ValueError, KeyError) as e:
            return f"Error formatting data: {e}"
    
    def clear_screen(self):
        """Clear the terminal screen"""
        import os
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def run_monitor(self, interval: int = 10):
        """
        Run the price monitoring loop
        
        Args:
            interval: Update interval in seconds (default: 10)
        """
        print("🚀 Starting SOLANA Price Monitor...")
        print(f"📊 Fetching data every {interval} seconds")
        print("⏹️  Press Ctrl+C to stop\n")
        
        try:
            while True:
                # Clear screen for clean display
                self.clear_screen()
                
                # Fetch and display price data
                price_data = self.get_price_data()
                
                if price_data:
                    formatted_data = self.format_price_data(price_data)
                    print(formatted_data)
                    print(f"\n⏰ Next update in {interval} seconds... (Press Ctrl+C to stop)")
                else:
                    print("❌ Failed to fetch price data. Retrying...")
                
                # Wait for next update
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n\n🛑 Monitor stopped by user")
            print("👋 Thank you for using SOLANA Price Monitor!")
            sys.exit(0)
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")
            sys.exit(1)


def main():
    """Main function"""
    print("=" * 60)
    print("🌟 SOLANA Price Monitor - OKX API")
    print("=" * 60)
    
    # Create monitor instance
    monitor = SolanaMonitor()
    
    # Test API connection first
    print("🔍 Testing API connection...")
    test_data = monitor.get_price_data()
    
    if test_data:
        print("✅ API connection successful!")
        current_price = test_data.get("last", "N/A")
        print(f"💰 Current SOL price: ${current_price} USDT")
        print()
        
        # Start monitoring
        monitor.run_monitor(interval=10)
    else:
        print("❌ Failed to connect to OKX API")
        print("🔧 Please check your internet connection and try again")
        sys.exit(1)


if __name__ == "__main__":
    main()
